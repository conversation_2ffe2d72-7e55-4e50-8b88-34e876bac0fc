/* 🎨 INTERFACE MODERNE AGENT DEEPSEEK R1 8B */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* === SIDEBAR === */
.sidebar {
    width: 350px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    overflow-y: auto;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-header h2 {
    font-size: 1.2em;
    margin-bottom: 10px;
    color: #4fc3f7;
}

.agent-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
    margin-bottom: 20px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #f44336;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #4caf50;
}

.status-dot.disconnected {
    background: #f44336;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* === MONITORING NEUROLOGIQUE === */
.brain-monitor {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.brain-monitor h3 {
    color: #ff9800;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.monitor-section {
    margin-bottom: 20px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.monitor-section h4 {
    color: #e1f5fe;
    margin-bottom: 10px;
    font-size: 0.9em;
}

/* Battement cardiaque */
.heartbeat-display {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2em;
    color: #f44336;
    font-weight: bold;
}

.heartbeat-visual {
    width: 20px;
    height: 20px;
    background: #f44336;
    border-radius: 50%;
    animation: heartbeat 1s infinite;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

.temperature-display {
    margin-top: 5px;
    color: #ff9800;
    font-weight: bold;
}

/* Ondes cérébrales */
.brainwaves {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.wave {
    display: flex;
    align-items: center;
    gap: 10px;
}

.wave span {
    width: 50px;
    font-size: 0.8em;
    color: #b3e5fc;
}

.wave-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.wave-fill {
    height: 100%;
    background: linear-gradient(90deg, #4fc3f7, #29b6f6);
    width: 0%;
    transition: width 0.3s ease;
}

.wave.active .wave-fill {
    background: linear-gradient(90deg, #ff9800, #f57c00);
    animation: waveActive 2s infinite;
}

@keyframes waveActive {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.dominant-wave {
    margin-top: 10px;
    color: #ff9800;
    font-weight: bold;
    font-size: 0.9em;
}

/* État émotionnel */
.emotion-display {
    text-align: center;
}

.current-emotion {
    font-size: 1.2em;
    color: #e91e63;
    font-weight: bold;
    margin-bottom: 5px;
}

.emotion-intensity {
    color: #b3e5fc;
    font-size: 0.9em;
}

/* Neurotransmetteurs */
.neurotransmitters {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.nt-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.nt-item span {
    font-size: 0.8em;
    color: #b3e5fc;
}

.nt-level {
    color: #4caf50;
    font-weight: bold;
    font-size: 0.9em;
}

/* Statistiques */
.stats-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
}

.stats-section h3 {
    color: #4caf50;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.stat-item span:first-child {
    color: #b3e5fc;
}

.stat-item span:last-child {
    color: #4caf50;
    font-weight: bold;
}

/* === ZONE DE CHAT === */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
}

.chat-header {
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.electron-btn {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    text-decoration: none;
}

.electron-btn:hover {
    background: linear-gradient(135deg, #f7931e, #ff6b35);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.electron-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
}

.electron-btn i {
    font-size: 16px;
}

.chat-header h1 {
    color: #333;
    font-size: 1.5em;
}

.connection-status {
    color: #666;
    font-size: 0.9em;
}

.connection-status.connected {
    color: #4caf50;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

/* Messages */
.message {
    display: flex;
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 1.2em;
}

.agent-message .message-avatar {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    color: white;
}

.message-content {
    flex: 1;
    max-width: calc(100% - 52px);
}

.message-text {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    line-height: 1.4;
    word-wrap: break-word;
}

.user-message .message-text {
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    color: white;
}

.message-time {
    font-size: 0.8em;
    color: #666;
    margin-top: 5px;
    padding-left: 16px;
}

/* Boutons d'action des messages */
.message-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    padding-left: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.message:hover .message-actions {
    opacity: 1;
}

.action-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.1);
    color: #666;
}

.action-button:hover {
    transform: scale(1.1);
    background: rgba(0, 0, 0, 0.2);
    color: #333;
}

.copy-button:hover {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.speak-button:hover {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.action-button:active {
    transform: scale(0.95);
}

/* États des boutons d'action */
.action-button.success {
    background: #4CAF50;
    color: white;
}

.action-button.error {
    background: #f44336;
    color: white;
}

.action-button.active {
    background: #2196F3;
    color: white;
    animation: actionActive 1s infinite;
}

@keyframes actionActive {
    0% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.7); }
    70% { box-shadow: 0 0 0 8px rgba(33, 150, 243, 0); }
    100% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0); }
}

/* Indicateur de réflexion */
.thinking-indicator {
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
    background: rgba(255, 255, 255, 0.8);
}

.thinking-animation {
    display: flex;
    gap: 4px;
}

.thinking-dot {
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
    animation: thinking 1.4s infinite ease-in-out;
}

.thinking-dot:nth-child(1) { animation-delay: -0.32s; }
.thinking-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Zone de saisie */
.chat-input-container {
    background: white;
    padding: 20px;
    border-top: 1px solid #e0e0e0;
}

.chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    max-width: 100%;
}

#messageInput {
    flex: 1;
    border: 2px solid #e0e0e0;
    border-radius: 20px;
    padding: 12px 16px;
    font-size: 16px;
    resize: none;
    outline: none;
    transition: border-color 0.3s ease;
    max-height: 120px;
    min-height: 44px;
}

#messageInput:focus {
    border-color: #667eea;
}

.send-button {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
}

.send-button:hover {
    transform: scale(1.05);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* NOUVEAU: Bouton Claude Direct */
.claude-direct-button {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(255, 107, 157, 0.3);
    margin-left: 10px;
    font-size: 16px;
}

.claude-direct-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 20px rgba(255, 107, 157, 0.4);
    background: linear-gradient(135deg, #ff8fab, #d63384);
}

.claude-direct-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* NOUVEAU: Messages de Claude */
.message.claude {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    border-left: 4px solid #ff8fab;
    box-shadow: 0 2px 15px rgba(255, 107, 157, 0.2);
}

.claude-header {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px 8px 0 0;
    padding: 8px 12px;
    margin: -12px -12px 8px -12px;
}

.claude-content {
    color: white;
    font-weight: 500;
}

/* NOUVEAU: Indicateur de réflexion Claude */
.claude-thinking {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    border-radius: 20px;
    padding: 15px 20px;
    margin: 10px 0;
    box-shadow: 0 2px 15px rgba(255, 107, 157, 0.2);
    animation: pulse 2s infinite;
}

.claude-thinking .thinking-content {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
}

.claude-thinking .thinking-avatar {
    font-size: 20px;
    animation: bounce 1s infinite;
}

.claude-thinking .thinking-text {
    font-weight: 500;
    flex: 1;
}

.claude-thinking .thinking-dots {
    display: flex;
    gap: 4px;
}

.claude-thinking .thinking-dots span {
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    animation: dot-pulse 1.5s infinite;
}

.claude-thinking .thinking-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.claude-thinking .thinking-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes dot-pulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

/* Messages d'erreur */
.error-message .message-text {
    background: #ffebee;
    color: #c62828;
    border-left: 4px solid #f44336;
}

/* Messages système */
.system-message .message-text {
    background: #e8f5e8;
    color: #2e7d32;
    border-left: 4px solid #4caf50;
}

/* Réflexion de l'agent */
.reflection-section {
    margin-top: 10px;
    padding: 10px;
    background: rgba(103, 126, 234, 0.1);
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

.reflection-title {
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.reflection-content {
    font-size: 0.85em;
    color: #555;
    line-height: 1.3;
}

/* Mémoire utilisée */
.memory-section {
    margin-top: 10px;
    padding: 8px;
    background: rgba(255, 152, 0, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ff9800;
}

.memory-title {
    font-weight: bold;
    color: #ff9800;
    margin-bottom: 5px;
    font-size: 0.85em;
}

.memory-items {
    font-size: 0.8em;
    color: #666;
}

.memory-item {
    margin-bottom: 3px;
}

/* === SYSTÈME COGNITIF KYBER ULTRA === */
.cognitive-system {
    background: rgba(0, 0, 0, 0.95);
    color: white;
    padding: 20px;
    border-top: 1px solid #333;
    max-height: 300px;
    overflow-y: auto;
}

.cognitive-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #333;
    padding-bottom: 10px;
}

.cognitive-header h3 {
    color: #00ff88;
    font-size: 1.1em;
}

.kyber-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.kyber-boost {
    font-size: 1.5em;
    font-weight: bold;
    color: #ff6b35;
    text-shadow: 0 0 10px #ff6b35;
    animation: kyberPulse 2s infinite;
}

.kyber-label {
    font-size: 0.8em;
    color: #888;
}

@keyframes kyberPulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

.cognitive-steps {
    margin-bottom: 15px;
}

.cognitive-step {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    margin-bottom: 5px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    border-left: 3px solid #00ff88;
    animation: stepAppear 0.3s ease;
}

@keyframes stepAppear {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

.step-icon {
    font-size: 1.2em;
    color: #00ff88;
}

.step-text {
    flex: 1;
    font-size: 0.9em;
}

.step-delay {
    font-size: 0.8em;
    color: #888;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

.neural-activity {
    border-top: 1px solid #333;
    padding-top: 15px;
}

.neural-visualization {
    height: 100px;
    background: radial-gradient(circle, rgba(0, 255, 136, 0.1) 0%, transparent 70%);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.neuron-network {
    width: 100%;
    height: 100%;
    position: relative;
}

.neuron {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #00ff88;
    border-radius: 50%;
    animation: neuronPulse 3s infinite;
}

@keyframes neuronPulse {
    0% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.5); }
    100% { opacity: 0.3; transform: scale(1); }
}

/* === CONTRÔLES AUDIO === */
.audio-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

.audio-button {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    transition: all 0.3s ease;
    position: relative;
}

.mic-button {
    background: linear-gradient(135deg, #e91e63, #ad1457);
    color: white;
}

.mic-button:hover {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(233, 30, 99, 0.5);
}

.mic-button.active {
    animation: micActive 1s infinite;
}

@keyframes micActive {
    0% { box-shadow: 0 0 0 0 rgba(233, 30, 99, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(233, 30, 99, 0); }
    100% { box-shadow: 0 0 0 0 rgba(233, 30, 99, 0); }
}

.speaker-button {
    background: linear-gradient(135deg, #2196f3, #1565c0);
    color: white;
}

.speaker-button:hover {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(33, 150, 243, 0.5);
}

.speaker-button.muted {
    background: linear-gradient(135deg, #757575, #424242);
}

.audio-status {
    flex: 1;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    text-align: center;
}

.audio-indicator {
    font-size: 0.9em;
    color: #333;
    font-weight: 500;
}

/* === CONTRÔLES KYBER === */
.kyber-controls {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: center;
}

.kyber-button, .cognitive-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 600;
    transition: all 0.3s ease;
}

.kyber-button {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
}

.kyber-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

.kyber-button.active {
    animation: kyberActive 2s infinite;
}

@keyframes kyberActive {
    0% { box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 107, 53, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 107, 53, 0); }
}

.cognitive-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.cognitive-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.cognitive-button.active {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
}

/* === CONTRÔLES DOCUMENTS === */
.document-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    border: 1px dashed #ddd;
}

.document-button, .clear-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85em;
    font-weight: 500;
    transition: all 0.3s ease;
}

.document-button {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.document-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.clear-button {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

.clear-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(244, 67, 54, 0.3);
}

.document-info {
    flex: 1;
    text-align: right;
    font-size: 0.8em;
    color: #666;
}

.document-stats {
    background: rgba(76, 175, 80, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    color: #4CAF50;
    font-weight: 500;
}

/* Zone de texte étendue pour documents */
.chat-input-wrapper textarea.document-mode {
    min-height: 150px;
    max-height: 400px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    background: #f8f9fa;
    border: 2px solid #4CAF50;
}

.chat-input-wrapper textarea.document-mode::placeholder {
    color: #4CAF50;
    font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 280px;
    }

    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: 200px;
    }

    .audio-controls {
        flex-direction: column;
        gap: 8px;
    }

    .cognitive-system {
        max-height: 200px;
    }

    .memory-monitor {
        width: 90%;
        right: 5%;
        top: 60px;
    }
}

/* === MONITEUR DE MÉMOIRE THERMIQUE === */
.memory-monitor {
    position: fixed;
    top: 10px;
    right: 420px;
    width: 320px;
    background: rgba(0, 0, 0, 0.95);
    border-radius: 15px;
    padding: 15px;
    color: white;
    font-size: 12px;
    z-index: 9999;
    border: 2px solid #00ff88;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
    max-height: 80vh;
    overflow-y: auto;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.memory-monitor.collapsed {
    width: 200px;
    height: 60px;
    overflow: hidden;
}

.memory-monitor.collapsed .memory-content {
    display: none;
}

.memory-monitor.collapsed {
    height: 40px;
    overflow: hidden;
}

.memory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    cursor: pointer;
}

.memory-title {
    font-weight: bold;
    color: #00ff88;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.memory-toggle {
    background: none;
    border: none;
    color: #00ff88;
    cursor: pointer;
    font-size: 16px;
    transition: transform 0.2s ease;
}

.memory-toggle:hover {
    transform: scale(1.1);
}

.memory-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.memory-stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.memory-stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #00ff88;
}

.memory-stat-label {
    font-size: 10px;
    color: #ccc;
    margin-top: 2px;
}

.memory-zones {
    margin-bottom: 15px;
}

.zone-item {
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 8px;
    padding: 10px;
    border-radius: 8px;
    border-left: 4px solid #00ff88;
    transition: all 0.3s ease;
}

.zone-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #ffd93d;
}

.zone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.zone-name {
    font-weight: bold;
    color: #00ff88;
    text-transform: uppercase;
    font-size: 11px;
}

.zone-temp {
    color: #ff6b6b;
    font-size: 10px;
}

.zone-progress {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
}

.zone-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #00ff88, #00cc6a);
    transition: width 0.3s ease;
}

.zone-info {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #ccc;
}

.memory-activity {
    max-height: 150px;
    overflow-y: auto;
}

.activity-title {
    font-weight: bold;
    color: #00ff88;
    margin-bottom: 8px;
    font-size: 12px;
}

.activity-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 6px;
    margin-bottom: 4px;
    border-radius: 4px;
    font-size: 10px;
    border-left: 2px solid #ffd93d;
}

.activity-type {
    color: #ffd93d;
    font-weight: bold;
    text-transform: uppercase;
}

.activity-details {
    color: #ccc;
    margin-top: 2px;
}

/* Animations pour les changements */
.zone-item.updated {
    animation: memoryPulse 0.5s ease;
}

@keyframes memoryPulse {
    0% { border-left-color: #00ff88; }
    50% { border-left-color: #ffd93d; }
    100% { border-left-color: #00ff88; }
}

.activity-item.new {
    animation: activitySlide 0.3s ease;
}

@keyframes activitySlide {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}
